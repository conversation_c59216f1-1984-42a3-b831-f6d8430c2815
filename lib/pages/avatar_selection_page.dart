import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../theme/app_design_system.dart';
import '../services/avatar_service.dart';

class AvatarSelectionPage extends StatefulWidget {
  final String? currentAvatar;

  const AvatarSelectionPage({
    super.key,
    this.currentAvatar,
  });

  @override
  State<AvatarSelectionPage> createState() => _AvatarSelectionPageState();
}

class _AvatarSelectionPageState extends State<AvatarSelectionPage> {
  String? _selectedAvatar;
  bool _isLoading = false;
  List<String> _availableAvatars = [];

  @override
  void initState() {
    super.initState();
    _selectedAvatar = widget.currentAvatar;
    _loadAvatars();
  }

  void _loadAvatars() {
    setState(() {
      _isLoading = true;
    });

    try {
      _availableAvatars = AvatarService.getAvailableAvatars();

      // 如果当前头像不在可用列表中，清除选择
      if (_selectedAvatar != null &&
          !AvatarService.isValidAvatarPath(_selectedAvatar!)) {
        _selectedAvatar = null;
      }
    } catch (e) {
      // 处理加载错误
      _availableAvatars = [];
      _showErrorMessage(context.mounted ? AppLocalizations.of(context)!.avatarLoadError : 'Failed to load avatars');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          l10n.selectAvatar,
          style: TextStyle(
            color: Colors.white,
            fontSize: AppDesignSystem.fontSizeLarge,
            fontWeight: AppDesignSystem.fontWeightSemiBold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton(
              onPressed: _selectedAvatar != null ? _saveAvatar : null,
              style: TextButton.styleFrom(
                backgroundColor: _selectedAvatar != null
                    ? AppDesignSystem.primaryYellow
                    : Colors.grey.withValues(alpha: 0.3),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDesignSystem.radiusM),
                ),
              ),
              child: Text(
                l10n.save,
                style: TextStyle(
                  fontSize: AppDesignSystem.fontSizeMedium,
                  fontWeight: AppDesignSystem.fontWeightSemiBold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
        ? const Center(
            child: CircularProgressIndicator(
              color: Colors.blue,
            ),
          )
        : _availableAvatars.isEmpty
          ? Center(
              child: Text(
                l10n.noAvatarsAvailable,
                style: const TextStyle(color: Colors.white),
              ),
            )
          : Padding(
              padding: EdgeInsets.all(AppDesignSystem.spaceL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: AppDesignSystem.spaceS,
                      bottom: AppDesignSystem.spaceM,
                    ),
                    child: Text(
                      l10n.chooseYourAvatar,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: AppDesignSystem.fontSizeLarge,
                        fontWeight: AppDesignSystem.fontWeightSemiBold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: GridView.builder(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: AppDesignSystem.spaceM,
                        mainAxisSpacing: AppDesignSystem.spaceM,
                        childAspectRatio: 1,
                      ),
                itemCount: _availableAvatars.length,
                itemBuilder: (context, index) {
                  final avatarPath = _availableAvatars[index];
            final isSelected = _selectedAvatar == avatarPath;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              child: GestureDetector(
                onTap: () => _selectAvatar(avatarPath),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppDesignSystem.radiusL),
                    border: Border.all(
                      color: isSelected
                          ? AppDesignSystem.primaryYellow
                          : Colors.grey.withValues(alpha: 0.3),
                      width: isSelected ? 3 : 1,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: AppDesignSystem.primaryYellow.withValues(alpha: 0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 1,
                      ),
                    ] : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppDesignSystem.radiusL - 1),
                        child: Image.asset(
                          avatarPath,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[800],
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.broken_image,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    AppLocalizations.of(context)!.avatarLoadError,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                      if (isSelected)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: AppDesignSystem.primaryYellow,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
                ],
              ),
            ),
    );
  }

  void _selectAvatar(String avatarPath) {
    try {
      if (AvatarService.isValidAvatarPath(avatarPath)) {
        setState(() {
          _selectedAvatar = avatarPath;
        });
      } else {
        _showErrorMessage(AppLocalizations.of(context)!.avatarSelectionError);
      }
    } catch (e) {
      _showErrorMessage(AppLocalizations.of(context)!.avatarSelectionError);
    }
  }

  void _saveAvatar() {
    try {
      if (_selectedAvatar != null && AvatarService.isValidAvatarPath(_selectedAvatar!)) {
        Navigator.pop(context, _selectedAvatar);
      } else {
        _showErrorMessage(AppLocalizations.of(context)!.avatarSelectionError);
      }
    } catch (e) {
      _showErrorMessage(AppLocalizations.of(context)!.avatarSelectionError);
    }
  }
}
