import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:easy_refresh/easy_refresh.dart';
import '../models/user.dart';
import '../services/ai_data_service.dart';
import '../components/tag_chip.dart';
import 'chat_page.dart';
import 'ai_detail_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AIRolePage extends StatefulWidget {
  const AIRolePage({super.key});

  @override
  State<AIRolePage> createState() => _AIRolePageState();
}

class _AIRolePageState extends State<AIRolePage> with AutomaticKeepAliveClientMixin {
  final PageController _pageController = PageController();
  final EasyRefreshController _refreshController = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );
  
  List<User> _aiUsers = [];
  int _currentIndex = 0;
  bool _isRefreshing = false;
  bool _isLoading = false;
  bool _hasLoadedData = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadAIUsersIfNeeded();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadAIUsersIfNeeded() async {
    if (_hasLoadedData) return;
    await _loadAIUsers();
  }

  Future<void> _loadAIUsers() async {
    final users = await AIDataService().getAllUsers();
    users.shuffle();
    if (mounted) {
      setState(() {
        _aiUsers = users;
        _currentIndex = 0;
        _hasLoadedData = true;
      });
    }
  }

  void _onPageChanged(int index) {
    if (mounted) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Future<void> _onRefresh() async {
    if (_isRefreshing) return;
    
    if (mounted) {
      setState(() {
        _isRefreshing = true;
      });
    }

    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // Refresh data and get new random order
      await AIDataService().refreshData();
      final users = await AIDataService().getAllUsers();
      users.shuffle();
      
      if (mounted) {
        setState(() {
          _aiUsers = users;
          _currentIndex = 0;
          // 刷新时不需要重置_hasLoadedData，保持已加载状态
        });
      }

      // Animate back to first page
      if (_pageController.hasClients) {
        await _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
      
      _refreshController.finishRefresh(IndicatorResult.success);
    } catch (e) {
      _refreshController.finishRefresh(IndicatorResult.fail);
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  Future<void> _onLoad() async {
    if (_isLoading) return;
    
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // 模拟加载延迟
      await Future.delayed(const Duration(milliseconds: 800));
      
      // 由于我们有限的AI角色，显示没有更多数据
      _refreshController.finishLoad(IndicatorResult.noMore);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: _aiUsers.isEmpty
          ? const Center(
              child: CircularProgressIndicator(
                color: Colors.blue,
              ),
            )
          : Stack(
              children: [
                EasyRefresh.builder(
                  controller: _refreshController,
                  onRefresh: _onRefresh,
                  onLoad: _onLoad,
                  header: ClassicHeader(
                    dragText: AppLocalizations.of(context)!.pullToRefresh,
                    armedText: AppLocalizations.of(context)!.releaseToRefresh,
                    readyText: AppLocalizations.of(context)!.refreshing,
                    processingText: AppLocalizations.of(context)!.refreshing,
                    processedText: AppLocalizations.of(context)!.refreshCompleted,
                    noMoreText: AppLocalizations.of(context)!.noMoreData,
                    failedText: AppLocalizations.of(context)!.refreshFailed,
                    messageText: AppLocalizations.of(context)!.lastUpdatedAt,
                    backgroundColor: Colors.transparent,
                    textStyle: TextStyle(color: Colors.grey.withOpacity(0.7)),
                    messageStyle: TextStyle(color: Colors.grey.withOpacity(0.5), fontSize: 12),
                    iconTheme: IconThemeData(color: Colors.grey.withOpacity(0.7)),
                    showMessage: false,
                  ),
                  footer: ClassicFooter(
                    dragText: 'Pull up to load more',
                    armedText: 'Release to load more',
                    readyText: 'Loading...',
                    processingText: 'Loading...',
                    processedText: 'Load completed',
                    noMoreText: '-- No Data --',
                    failedText: 'Load failed',
                    backgroundColor: Colors.transparent,
                    textStyle: TextStyle(
                      color: Colors.grey.withOpacity(0.7),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1,
                    ),
                    iconTheme: const IconThemeData(color: Colors.transparent, size: 0),
                    infiniteOffset: null,
                  ),
                  childBuilder: (context, physics) {
                    return Container(
                      height: MediaQuery.of(context).size.height,
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: _onPageChanged,
                        scrollDirection: Axis.vertical,
                        physics: physics,
                        itemCount: _aiUsers.length,
                        itemBuilder: (context, index) {
                          final user = _aiUsers[index];
                          return _buildUserCard(user, index);
                        },
                      ),
                    );
                  },
                ),

                // Fixed header
                Positioned(
                  top: 50,
                  left: 20,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.aiRole,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),

                // Page indicator (only show in debug mode)
                // if (kDebugMode)
                //   Positioned(
                //     top: 120,
                //     right: 20,
                //     child: Container(
                //       padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                //       decoration: BoxDecoration(
                //         color: Colors.black.withOpacity(0.6),
                //         borderRadius: BorderRadius.circular(15),
                //         border: Border.all(
                //           color: Colors.white.withOpacity(0.2),
                //           width: 1,
                //         ),
                //       ),
                //       child: Text(
                //         '${_currentIndex + 1}/${_aiUsers.length}',
                //         style: const TextStyle(
                //           color: Colors.white70,
                //           fontSize: 12,
                //           fontWeight: FontWeight.w500,
                //         ),
                //       ),
                //     ),
                //   ),


              ],
            ),
    );
  }

  Widget _buildUserCard(User user, int index) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(user.avatar),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Dark overlay for better text readability
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),
          
          // Content
          Positioned(
            bottom: 100,
            left: 20,
            right: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User info
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AIDetailPage(aiUser: user),
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Stack(
                        children: [
                          Hero(
                            tag: 'user_avatar_${user.id}',
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(30),
                                image: DecorationImage(
                                  image: AssetImage(user.avatar),
                                  fit: BoxFit.cover,
                                ),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.9),
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                          if (user.isOnline)
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 1),
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user.getRoleAndPersonality(context),
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Description
                Text(
                  user.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 16),
                
                // Tags
                if (user.tags.isNotEmpty)
                  TagChipList(
                    tags: user.tags,
                    maxTags: 3,
                  ),
                
                const SizedBox(height: 20),
                
                // Chat button
                Container(
                  width: double.infinity,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.8),
                      width: 1.5,
                    ),
                  ),
                  child: MaterialButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ChatPage(aiUser: user),
                        ),
                      );
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.chat_bubble_outline,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context)!.chatNow,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


} 