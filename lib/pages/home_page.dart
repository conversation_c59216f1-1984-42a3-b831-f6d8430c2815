import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import 'ai_role_page.dart';
import 'chat_list_page.dart';
import 'profile_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const AIRolePage(),
    const ChatListPage(),
    const ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: Container(
        height: 130,
        decoration: const BoxDecoration(
          color: Color(0xFF0A0A0A),
          border: Border(
            top: BorderSide(
              color: Color(0xFF1C1C1E),
              width: 0.5,
            ),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          backgroundColor: Color(0xFF0A0A0A),
          selectedItemColor: Colors.transparent,
          unselectedItemColor: Colors.transparent,
          type: BottomNavigationBarType.fixed,
          elevation: 0,
          items: [
            BottomNavigationBarItem(
              icon: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                  color: _currentIndex == 0 ? AppDesignSystem.primaryYellow: null,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: _currentIndex == 0 ? [
                    BoxShadow(
                      color: const Color(0xFF667eea).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Icon(
                  Icons.stars,
                  size: 28,
                  color: _currentIndex == 0 ? AppDesignSystem.primaryBlack : Colors.white,
                ),
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                  color: _currentIndex == 1 ? AppDesignSystem.primaryYellow : null,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: _currentIndex == 1 ? [
                    BoxShadow(
                      color: const Color(0xFF667eea).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Icon(
                  Icons.forum,
                  size: 28,
                  color: _currentIndex == 1 ? AppDesignSystem.primaryBlack : Colors.white,
                ),
              ),
              label: '',
            ),
            BottomNavigationBarItem(
              icon: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 8),
                decoration: BoxDecoration(
                  color: _currentIndex == 2 ? AppDesignSystem.primaryYellow : null,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: _currentIndex == 2 ? [
                    BoxShadow(
                      color: const Color(0xFF667eea).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Icon(
                  Icons.account_circle,
                  size: 28,
                  color: _currentIndex == 2 ? AppDesignSystem.primaryBlack : Colors.white,
                ),
              ),
              label: '',
            ),
          ],
        ),
      ),
    );
  }
} 