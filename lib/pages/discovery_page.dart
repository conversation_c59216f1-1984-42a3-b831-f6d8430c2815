import 'package:flutter/material.dart';
import 'package:easy_refresh/easy_refresh.dart';
import '../theme/app_design_system.dart';
import '../components/grid_background.dart';
import '../components/tag_chip.dart';
import '../models/user.dart';
import '../services/ai_data_service.dart';
import 'chat_page.dart';
import 'ai_detail_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class DiscoveryPage extends StatefulWidget {
  const DiscoveryPage({super.key});

  @override
  State<DiscoveryPage> createState() => _DiscoveryPageState();
}

class _DiscoveryPageState extends State<DiscoveryPage>
    with AutomaticKeepAliveClientMixin {
  late EasyRefreshController _refreshController;
  
  List<User> _weeklyStars = [];
  List<User> _entertainmentUsers = [];
  List<User> _learningUsers = [];
  List<User> _workUsers = [];
  List<String> _hotTopics = [];
  bool _isLoading = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _refreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
    _loadDiscoveryData();
  }

  /// 获取国际化的热门话题列表
  List<String> _getLocalizedHotTopics() {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) {
      // 如果无法获取本地化，返回默认的英文话题
      return [
        'Future of AI Development',
        'How to Improve Work Efficiency',
        'Creative Writing Techniques',
        'Healthy Lifestyle',
        'Technology and Life',
        'Personal Growth and Development',
      ];
    }

    return [
      l10n.topicAIFuture,
      l10n.topicProductivity,
      l10n.topicCreativeWriting,
      l10n.topicHealthyLifestyle,
      l10n.topicTechLife,
      l10n.topicPersonalGrowth,
    ];
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadDiscoveryData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      // 加载本周之星（前5个用户）
      final allUsers = await AIDataService().getAllUsers();
      _weeklyStars = allUsers.take(5).toList();
      
      // 按分类加载用户
      _entertainmentUsers = allUsers.where((user) => 
        user.tags.any((tag) => tag.toLowerCase().contains('entertainment') || 
                               tag.toLowerCase().contains('fun') ||
                               tag.toLowerCase().contains('game'))).take(4).toList();
      
      _learningUsers = allUsers.where((user) => 
        user.tags.any((tag) => tag.toLowerCase().contains('education') || 
                               tag.toLowerCase().contains('learn') ||
                               tag.toLowerCase().contains('study'))).take(4).toList();
      
      _workUsers = allUsers.where((user) => 
        user.tags.any((tag) => tag.toLowerCase().contains('business') || 
                               tag.toLowerCase().contains('work') ||
                               tag.toLowerCase().contains('professional'))).take(4).toList();
      
      // 获取国际化的热门话题
      _hotTopics = _getLocalizedHotTopics();
      
    } catch (e) {
      debugPrint('Error loading discovery data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onRefresh() async {
    await _loadDiscoveryData();
    _refreshController.finishRefresh();
  }

  void _startChat(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(aiUser: user),
      ),
    );
  }

  void _viewUserDetail(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIDetailPage(aiUser: user),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      body: GridBackground(
        gridSize: 35.0,
        gridColor: Colors.black.withAlpha(20),
        strokeWidth: 0.5,
        child: EasyRefresh(
          controller: _refreshController,
          onRefresh: _onRefresh,
          child: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                backgroundColor: AppDesignSystem.backgroundPrimary,
                elevation: 0,
                floating: true,
                snap: true,
                title: Text(
                  AppLocalizations.of(context)!.discovery,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                automaticallyImplyLeading: false,
              ),
              
              // 本周之星模块
              _buildWeeklyStarsSection(),
              
              // 按分类推荐模块
              _buildCategoryRecommendationsSection(),
              
              // 热门话题模块
              _buildHotTopicsSection(),
              
              // 底部间距
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeeklyStarsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context)!.weeklyStars,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: 导航到完整的本周之星页面
                  },
                  child: Text(
                    AppLocalizations.of(context)!.viewMore,
                    style: TextStyle(
                      color: AppDesignSystem.primaryYellowAccent,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const SizedBox(
                height: 120,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_weeklyStars.isEmpty)
              SizedBox(
                height: 120,
                child: Center(
                  child: Text(
                    '暂无推荐内容',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ),
              )
            else
              _buildWeeklyStarBanner(_weeklyStars.first),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyStarBanner(User user) {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppDesignSystem.primaryYellow.withValues(alpha: 0.1),
            AppDesignSystem.primaryYellow.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppDesignSystem.primaryYellow.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _viewUserDetail(user),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 头像
                CircleAvatar(
                  radius: 35,
                  backgroundImage: AssetImage(user.avatar),
                ),
                const SizedBox(width: 16),

                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 本周之星标签
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppDesignSystem.primaryYellow,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.popularThisWeek,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 6),

                      // 名称
                      Text(
                        user.name,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),

                      // 角色描述
                      Text(
                        user.getRoleAndPersonality(context),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // 开始聊天按钮
                ElevatedButton(
                  onPressed: () => _startChat(user),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppDesignSystem.primaryYellow,
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    elevation: 0,
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.startChat,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryRecommendationsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.categoryRecommendations,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // 娱乐分类
            _buildCategorySection(
              AppLocalizations.of(context)!.entertainment,
              _entertainmentUsers,
              Icons.movie,
            ),
            const SizedBox(height: 24),
            
            // 学习分类
            _buildCategorySection(
              AppLocalizations.of(context)!.learning,
              _learningUsers,
              Icons.school,
            ),
            const SizedBox(height: 24),
            
            // 工作分类
            _buildCategorySection(
              AppLocalizations.of(context)!.work,
              _workUsers,
              Icons.work,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection(String title, List<User> users, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppDesignSystem.primaryYellowAccent,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_isLoading)
          const SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        else if (users.isEmpty)
          SizedBox(
            height: 100,
            child: Center(
              child: Text(
                '暂无推荐内容',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
          )
        else
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return _buildCategoryUserCard(user);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildCategoryUserCard(User user) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      child: GestureDetector(
        onTap: () => _viewUserDetail(user),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 头像
            CircleAvatar(
              radius: 25,
              backgroundImage: AssetImage(user.avatar),
            ),
            const SizedBox(height: 6),

            // 名称
            Text(
              user.name,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotTopicsSection() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.hotTopics,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: _hotTopics.map((topic) => _buildTopicChip(topic)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicChip(String topic) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () {
          // TODO: 实现话题点击功能，可以导航到相关的AI角色或开始相关话题的聊天
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('点击了话题: $topic'),
              duration: const Duration(seconds: 1),
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppDesignSystem.primaryYellow,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Text(
            topic,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
