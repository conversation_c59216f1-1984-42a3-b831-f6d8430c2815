import 'package:flutter/material.dart';
import 'app_design_system.dart';

/// 设计系统使用示例
/// 展示如何在组件中使用设计规范
class DesignSystemExamples extends StatelessWidget {
  const DesignSystemExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      appBar: AppBar(
        title: const Text('设计系统示例'),
        backgroundColor: AppDesignSystem.backgroundPrimary,
      ),
      body: SingleChildScrollView(
        padding: AppDesignSystem.paddingM,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildColorSection(),
            const SizedBox(height: AppDesignSystem.spaceXL),
            _buildTypographySection(),
            const SizedBox(height: AppDesignSystem.spaceXL),
            _buildButtonSection(),
            const SizedBox(height: AppDesignSystem.spaceXL),
            _buildCardSection(),
            const SizedBox(height: AppDesignSystem.spaceXL),
            _buildInputSection(),
            const SizedBox(height: AppDesignSystem.spaceXL),
            _buildSpacingSection(),
          ],
        ),
      ),
    );
  }

  /// 颜色示例
  Widget _buildColorSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '颜色系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        // 主色调
        _buildColorItem('主色调', AppDesignSystem.primaryYellow),
        _buildColorItem('主色调辅助', AppDesignSystem.primaryYellowAccent),
        
        // 背景色
        _buildColorItem('主背景', AppDesignSystem.backgroundPrimary),
        _buildColorItem('次背景', AppDesignSystem.backgroundSecondary),
        _buildColorItem('卡片背景', AppDesignSystem.backgroundCard),
        
        // 渐变示例
        const SizedBox(height: AppDesignSystem.spaceM),
        Container(
          height: 60,
          decoration: BoxDecoration(
            gradient: AppDesignSystem.primaryGradient,
            borderRadius: AppDesignSystem.borderRadiusL,
          ),
          child: const Center(
            child: Text(
              '主渐变色',
              style: TextStyle(
                color: AppDesignSystem.textOnPrimary,
                fontWeight: AppDesignSystem.fontWeightSemiBold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 字体示例
  Widget _buildTypographySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '字体系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        const Text('大标题', style: AppDesignSystem.headingLarge),
        const Text('中标题', style: AppDesignSystem.headingMedium),
        const Text('小标题', style: AppDesignSystem.headingSmall),
        const Text('正文大', style: AppDesignSystem.bodyLarge),
        const Text('正文中', style: AppDesignSystem.bodyMedium),
        const Text('正文小', style: AppDesignSystem.bodySmall),
        const Text('说明文字', style: AppDesignSystem.caption),
      ],
    );
  }

  /// 按钮示例
  Widget _buildButtonSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '按钮系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        // 主按钮
        Container(
          width: double.infinity,
          height: AppDesignSystem.buttonHeightL,
          decoration: AppDesignSystem.primaryButtonDecoration,
          child: const Center(
            child: Text(
              '主按钮',
              style: AppDesignSystem.buttonText,
            ),
          ),
        ),
        
        const SizedBox(height: AppDesignSystem.spaceM),
        
        // 次按钮
        Container(
          width: double.infinity,
          height: AppDesignSystem.buttonHeightL,
          decoration: AppDesignSystem.secondaryButtonDecoration,
          child: const Center(
            child: Text(
              '次按钮',
              style: TextStyle(
                color: AppDesignSystem.textPrimary,
                fontSize: AppDesignSystem.fontSizeMedium,
                fontWeight: AppDesignSystem.fontWeightSemiBold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 卡片示例
  Widget _buildCardSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '卡片系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        Container(
          width: double.infinity,
          decoration: AppDesignSystem.cardDecoration,
          padding: AppDesignSystem.paddingL,
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '卡片标题',
                style: AppDesignSystem.headingSmall,
              ),
              SizedBox(height: AppDesignSystem.spaceS),
              Text(
                '这是一个使用设计系统的卡片示例。它展示了如何使用统一的背景色、圆角、阴影和内边距。',
                style: AppDesignSystem.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 输入框示例
  Widget _buildInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '输入框系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        Container(
          decoration: AppDesignSystem.inputDecoration,
          child: const TextField(
            decoration: InputDecoration(
              hintText: '请输入内容...',
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDesignSystem.spaceL,
                vertical: AppDesignSystem.spaceS,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 间距示例
  Widget _buildSpacingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '间距系统',
          style: AppDesignSystem.headingMedium,
        ),
        const SizedBox(height: AppDesignSystem.spaceM),
        
        _buildSpacingItem('XS', AppDesignSystem.spaceXS),
        _buildSpacingItem('S', AppDesignSystem.spaceS),
        _buildSpacingItem('M', AppDesignSystem.spaceM),
        _buildSpacingItem('L', AppDesignSystem.spaceL),
        _buildSpacingItem('XL', AppDesignSystem.spaceXL),
        _buildSpacingItem('XXL', AppDesignSystem.spaceXXL),
      ],
    );
  }

  /// 颜色项
  Widget _buildColorItem(String name, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDesignSystem.spaceS),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: AppDesignSystem.borderRadiusS,
              border: Border.all(
                color: AppDesignSystem.borderSecondary,
                width: 1,
              ),
            ),
          ),
          const SizedBox(width: AppDesignSystem.spaceM),
          Text(
            name,
            style: AppDesignSystem.bodyMedium,
          ),
        ],
      ),
    );
  }

  /// 间距项
  Widget _buildSpacingItem(String name, double spacing) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDesignSystem.spaceS),
      child: Row(
        children: [
          Container(
            width: spacing,
            height: 20,
            decoration: BoxDecoration(
              color: AppDesignSystem.primaryYellow,
              borderRadius: AppDesignSystem.borderRadiusXS,
            ),
          ),
          const SizedBox(width: AppDesignSystem.spaceM),
          Text(
            '$name (${spacing}px)',
            style: AppDesignSystem.bodyMedium,
          ),
        ],
      ),
    );
  }
}

/// 使用示例代码片段
class DesignSystemUsageExamples {
  /// 如何使用颜色
  static const String colorUsage = '''
// 使用主色调
Container(
  color: AppDesignSystem.primaryYellow,
  child: Text(
    'Hello',
    style: TextStyle(color: AppDesignSystem.textOnPrimary),
  ),
)

// 使用渐变
Container(
  decoration: BoxDecoration(
    gradient: AppDesignSystem.primaryGradient,
    borderRadius: AppDesignSystem.borderRadiusL,
  ),
)
''';

  /// 如何使用字体
  static const String typographyUsage = '''
// 使用预定义文字样式
Text('标题', style: AppDesignSystem.headingLarge)
Text('正文', style: AppDesignSystem.bodyMedium)
Text('说明', style: AppDesignSystem.caption)

// 自定义文字样式
Text(
  'Custom Text',
  style: TextStyle(
    fontSize: AppDesignSystem.fontSizeLarge,
    fontWeight: AppDesignSystem.fontWeightBold,
    color: AppDesignSystem.textPrimary,
  ),
)
''';

  /// 如何使用间距
  static const String spacingUsage = '''
// 使用预定义间距
Padding(
  padding: AppDesignSystem.paddingM,
  child: child,
)

// 使用间距值
SizedBox(height: AppDesignSystem.spaceL)
EdgeInsets.symmetric(
  horizontal: AppDesignSystem.spaceM,
  vertical: AppDesignSystem.spaceS,
)
''';

  /// 如何使用装饰
  static const String decorationUsage = '''
// 使用卡片装饰
Container(
  decoration: AppDesignSystem.cardDecoration,
  child: child,
)

// 使用按钮装饰
Container(
  decoration: AppDesignSystem.primaryButtonDecoration,
  child: child,
)

// 自定义装饰
Container(
  decoration: AppDesignSystem.createGradientDecoration(
    colors: [Colors.red, Colors.blue],
    borderRadius: AppDesignSystem.borderRadiusL,
  ),
)
''';
}
