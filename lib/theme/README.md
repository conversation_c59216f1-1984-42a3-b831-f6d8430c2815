# 设计系统文档

本项目的设计系统统一管理了所有的设计规范，包括颜色、字体、间距、圆角、阴影等设计元素。

## 文件结构

```
lib/theme/
├── app_design_system.dart      # 设计系统核心文件
├── app_theme.dart             # 应用主题配置
├── design_system_examples.dart # 使用示例
└── README.md                  # 本文档
```

## 核心文件说明

### 1. AppDesignSystem (app_design_system.dart)

设计系统的核心文件，包含所有设计规范的定义：

- **颜色系统**: 主色调、背景色、文字颜色、状态颜色等
- **字体系统**: 字体大小、字重、预定义文字样式
- **间距系统**: 基于4px网格的间距规范
- **圆角系统**: 统一的圆角规范
- **阴影系统**: 不同层级的阴影效果
- **动画系统**: 统一的动画时长和曲线
- **尺寸系统**: 图标、头像、按钮等组件的标准尺寸
- **组件样式**: 预定义的组件装饰样式

### 2. AppTheme (app_theme.dart)

基于设计系统创建的完整应用主题配置：

- 深色主题（主要主题）
- 浅色主题（备用主题）
- 系统UI样式配置

### 3. DesignSystemExamples (design_system_examples.dart)

设计系统的使用示例和代码片段。

## 使用指南

### 1. 颜色使用

```dart
// 使用主色调
Container(
  color: AppDesignSystem.primaryYellow,
  child: Text(
    'Hello',
    style: TextStyle(color: AppDesignSystem.textOnPrimary),
  ),
)

// 使用渐变
Container(
  decoration: BoxDecoration(
    gradient: AppDesignSystem.primaryGradient,
    borderRadius: AppDesignSystem.borderRadiusL,
  ),
)

// 使用工具方法
Color tagColor = AppDesignSystem.getTagColorByFirstLetter('Flutter');
```

### 2. 字体使用

```dart
// 使用预定义文字样式
Text('标题', style: AppDesignSystem.headingLarge)
Text('正文', style: AppDesignSystem.bodyMedium)
Text('说明', style: AppDesignSystem.caption)

// 自定义文字样式
Text(
  'Custom Text',
  style: TextStyle(
    fontSize: AppDesignSystem.fontSizeLarge,
    fontWeight: AppDesignSystem.fontWeightBold,
    color: AppDesignSystem.textPrimary,
  ),
)
```

### 3. 间距使用

```dart
// 使用预定义间距
Padding(
  padding: AppDesignSystem.paddingM,
  child: child,
)

// 使用间距值
SizedBox(height: AppDesignSystem.spaceL)
EdgeInsets.symmetric(
  horizontal: AppDesignSystem.spaceM,
  vertical: AppDesignSystem.spaceS,
)
```

### 4. 装饰使用

```dart
// 使用卡片装饰
Container(
  decoration: AppDesignSystem.cardDecoration,
  child: child,
)

// 使用按钮装饰
Container(
  decoration: AppDesignSystem.primaryButtonDecoration,
  child: child,
)

// 使用标签装饰
Container(
  decoration: AppDesignSystem.tagChipDecoration(Colors.blue),
  child: child,
)
```

### 5. 动画使用

```dart
AnimatedContainer(
  duration: AppDesignSystem.animationNormal,
  curve: AppDesignSystem.animationCurveDefault,
  // ...
)
```

## 设计规范

### 颜色规范

- **主色调**: `#667eea` 到 `#764ba2` 的渐变
- **背景色**: 深色主题，主背景 `#0A0A0A`
- **文字颜色**: 白色系列，支持不同透明度
- **状态颜色**: 成功、错误、警告、信息

### 字体规范

- **字体大小**: 12px - 24px，基于4px网格
- **字重**: Light(300) - Bold(700)
- **行高**: 根据字体大小自动计算

### 间距规范

- **基础单位**: 4px
- **间距范围**: 4px - 56px
- **常用间距**: 8px, 12px, 16px, 20px, 24px

### 圆角规范

- **小圆角**: 4px - 8px
- **中圆角**: 12px - 16px
- **大圆角**: 20px - 28px
- **完全圆形**: 50px

### 阴影规范

- **轻阴影**: 用于悬浮元素
- **中阴影**: 用于卡片和按钮
- **重阴影**: 用于模态框和重要元素

## 最佳实践

### 1. 一致性

- 始终使用设计系统中定义的值
- 避免硬编码颜色、字体大小、间距等
- 保持组件样式的一致性

### 2. 可维护性

- 新增设计元素时，优先考虑添加到设计系统中
- 定期审查和优化设计系统
- 保持文档的及时更新

### 3. 扩展性

- 使用工具方法创建动态样式
- 支持主题切换和自定义
- 考虑国际化和无障碍访问

## 组件更新指南

当需要更新现有组件以使用设计系统时：

1. 导入设计系统：`import '../theme/app_design_system.dart';`
2. 替换硬编码的颜色、字体、间距等
3. 使用预定义的装饰和样式
4. 测试组件在不同主题下的表现

## 示例页面

运行 `DesignSystemExamples` 页面可以查看所有设计元素的实际效果：

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const DesignSystemExamples(),
  ),
);
```

## 注意事项

1. **版本兼容性**: 使用了 `withValues()` 方法替代已弃用的 `withOpacity()`
2. **性能优化**: 静态常量避免重复创建对象
3. **主题适配**: 支持深色和浅色主题切换
4. **国际化**: 考虑了RTL语言的支持

## 更新日志

- **v1.0.0**: 初始版本，包含完整的设计系统
- 支持深色主题
- 包含所有基础设计元素
- 提供完整的使用示例和文档
