import 'package:flutter/material.dart';

/// 应用设计系统 - 统一管理所有设计规范
/// 包含颜色、字体、间距、圆角、阴影等设计元素
class AppDesignSystem {
  AppDesignSystem._();

  // ==================== 颜色系统 ====================
  
  /// 主色调
  static const Color primaryYellow = Color(0xFFFFD700);
  static const Color primaryBlack = Color(0xFF2C2C2E);
  static const Color primaryYellowAccent = Color(0xFF764ba2);

  /// Profile页面专用颜色
  static const Color profileCardBackground = Color(0xFF000000);
  
  /// 渐变色
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryYellow, primaryYellowAccent],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
  
  static const LinearGradient primaryGradientVertical = LinearGradient(
    colors: [primaryYellow, primaryYellowAccent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  /// 背景色
  static const Color backgroundPrimary = Color(0xFFFBF8EF);
  static const Color backgroundSecondary = Colors.white;
  static const Color backgroundCard = Color(0xFFFFFDF6);
  
  /// 文字颜色
  static const Color textPrimary = Colors.black;
  static const Color textSecondary = Color(0xFFB5B5B5);
  static const Color textTertiary = Color(0x99FFFFFF); // white70
  static const Color textQuaternary = Color(0x66FFFFFF); // white60
  static const Color textHint = Colors.grey;
  static const Color textOnPrimary = Colors.white;
  static const Color textOnLight = Colors.black;
  
  /// 消息气泡颜色
  static const Color messageBubbleUser = Color(0xE6FFFFFF); // white.withOpacity(0.9)
  static const Color messageBubbleAI = Color(0xBF000000); // black.withOpacity(0.75)
  
  /// 状态颜色
  static const Color success = Colors.green;
  static const Color error = Colors.red;
  static const Color warning = Colors.orange;
  static const Color info = Colors.blue;
  static const Color onlineIndicator = Colors.green;
  
  /// 边框颜色
  static const Color borderPrimary = Color(0xFF1C1C1E);
  static const Color borderSecondary = Color(0x33FFFFFF); // white.withOpacity(0.2)
  
  // ==================== 字体系统 ====================
  
  /// 字体大小
  static const double fontSizeXXL = 24.0;
  static const double fontSizeXL = 20.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeRegular = 14.0;
  static const double fontSizeSmall = 13.0;
  static const double fontSizeXS = 12.0;
  
  /// 字重
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  
  /// 文字样式
  static const TextStyle headingLarge = TextStyle(
    fontSize: fontSizeXXL,
    fontWeight: fontWeightBold,
    color: textPrimary,
  );
  
  static const TextStyle headingMedium = TextStyle(
    fontSize: fontSizeXL,
    fontWeight: fontWeightBold,
    color: textPrimary,
  );
  
  static const TextStyle headingSmall = TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: fontWeightSemiBold,
    color: textPrimary,
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: fontWeightRegular,
    color: textPrimary,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: fontSizeRegular,
    fontWeight: fontWeightRegular,
    color: textPrimary,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: fontSizeSmall,
    fontWeight: fontWeightRegular,
    color: textSecondary,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: fontSizeXS,
    fontWeight: fontWeightRegular,
    color: textTertiary,
  );
  
  static const TextStyle buttonText = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: fontWeightSemiBold,
    color: textOnPrimary,
  );
  
  // ==================== 间距系统 ====================
  
  /// 基础间距单位
  static const double spaceUnit = 4.0;
  
  /// 间距值
  static const double spaceXXS = spaceUnit; // 4
  static const double spaceXS = spaceUnit * 2; // 8
  static const double spaceS = spaceUnit * 3; // 12
  static const double spaceM = spaceUnit * 4; // 16
  static const double spaceL = spaceUnit * 5; // 20
  static const double spaceXL = spaceUnit * 6; // 24
  static const double spaceXXL = spaceUnit * 7; // 28
  static const double space3XL = spaceUnit * 8; // 32
  static const double space4XL = spaceUnit * 10; // 40
  static const double space5XL = spaceUnit * 12; // 48
  static const double space6XL = spaceUnit * 14; // 56
  
  /// 常用边距
  static const EdgeInsets paddingXS = EdgeInsets.all(spaceXS);
  static const EdgeInsets paddingS = EdgeInsets.all(spaceS);
  static const EdgeInsets paddingM = EdgeInsets.all(spaceM);
  static const EdgeInsets paddingL = EdgeInsets.all(spaceL);
  static const EdgeInsets paddingXL = EdgeInsets.all(spaceXL);
  
  /// 水平边距
  static const EdgeInsets paddingHorizontalS = EdgeInsets.symmetric(horizontal: spaceS);
  static const EdgeInsets paddingHorizontalM = EdgeInsets.symmetric(horizontal: spaceM);
  static const EdgeInsets paddingHorizontalL = EdgeInsets.symmetric(horizontal: spaceL);
  static const EdgeInsets paddingHorizontalXL = EdgeInsets.symmetric(horizontal: spaceXL);
  
  /// 垂直边距
  static const EdgeInsets paddingVerticalS = EdgeInsets.symmetric(vertical: spaceS);
  static const EdgeInsets paddingVerticalM = EdgeInsets.symmetric(vertical: spaceM);
  static const EdgeInsets paddingVerticalL = EdgeInsets.symmetric(vertical: spaceL);
  static const EdgeInsets paddingVerticalXL = EdgeInsets.symmetric(vertical: spaceXL);
  
  // ==================== 圆角系统 ====================
  
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusRound = 28.0; // 用于按钮等圆形元素
  static const double radiusCircle = 50.0; // 完全圆形
  
  /// 常用圆角
  static const BorderRadius borderRadiusXS = BorderRadius.all(Radius.circular(radiusXS));
  static const BorderRadius borderRadiusS = BorderRadius.all(Radius.circular(radiusS));
  static const BorderRadius borderRadiusM = BorderRadius.all(Radius.circular(radiusM));
  static const BorderRadius borderRadiusL = BorderRadius.all(Radius.circular(radiusL));
  static const BorderRadius borderRadiusXL = BorderRadius.all(Radius.circular(radiusXL));
  static const BorderRadius borderRadiusXXL = BorderRadius.all(Radius.circular(radiusXXL));
  static const BorderRadius borderRadiusRound = BorderRadius.all(Radius.circular(radiusRound));
  
  // ==================== 阴影系统 ====================
  
  /// 基础阴影
  static final BoxShadow shadowLight = BoxShadow(
    color: Colors.black.withValues(alpha: 0.08),
    blurRadius: 3.0,
    offset: const Offset(0, 1),
  );

  static final BoxShadow shadowMedium = BoxShadow(
    color: Colors.black.withValues(alpha: 0.1),
    blurRadius: 8.0,
    offset: const Offset(0, 4),
  );

  static final BoxShadow shadowHeavy = BoxShadow(
    color: Colors.black.withValues(alpha: 0.15),
    blurRadius: 20.0,
    offset: const Offset(0, 10),
  );

  /// 主色调阴影
  static final BoxShadow shadowPrimary = BoxShadow(
    color: primaryYellow.withValues(alpha: 0.3),
    blurRadius: 8.0,
    offset: const Offset(0, 4),
  );

  static final BoxShadow shadowPrimaryLight = BoxShadow(
    color: primaryYellow.withValues(alpha: 0.3),
    blurRadius: 6.0,
    offset: const Offset(0, 2),
  );
  
  // ==================== 动画系统 ====================
  
  /// 动画时长
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 200);
  static const Duration animationSlow = Duration(milliseconds: 300);
  static const Duration animationSlower = Duration(milliseconds: 600);
  
  /// 动画曲线
  static const Curve animationCurveDefault = Curves.easeInOut;
  static const Curve animationCurveEaseOut = Curves.easeOut;
  static const Curve animationCurveEaseIn = Curves.easeIn;
  
  // ==================== 尺寸系统 ====================
  
  /// 图标尺寸
  static const double iconSizeXS = 16.0;
  static const double iconSizeS = 18.0;
  static const double iconSizeM = 20.0;
  static const double iconSizeL = 24.0;
  static const double iconSizeXL = 32.0;
  static const double iconSizeXXL = 48.0;
  
  /// 头像尺寸
  static const double avatarSizeS = 32.0;
  static const double avatarSizeM = 40.0;
  static const double avatarSizeL = 56.0;
  static const double avatarSizeXL = 80.0;
  
  /// 按钮高度
  static const double buttonHeightS = 36.0;
  static const double buttonHeightM = 44.0;
  static const double buttonHeightL = 56.0;
  
  /// 输入框高度
  static const double inputHeightS = 36.0;
  static const double inputHeightM = 44.0;
  static const double inputHeightL = 56.0;
  
  // ==================== 透明度系统 ====================

  static const double opacityDisabled = 0.3;
  static const double opacityMedium = 0.6;
  static const double opacityHigh = 0.8;
  static const double opacityFull = 1.0;

  // ==================== 组件样式系统 ====================

  /// 卡片装饰
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: backgroundCard,
    borderRadius: borderRadiusL,
    boxShadow: [shadowMedium],
  );

  /// 输入框装饰
  static BoxDecoration get inputDecoration => BoxDecoration(
    color: Colors.white.withValues(alpha: 0.9),
    borderRadius: borderRadiusRound,
  );

  /// 底部导航栏装饰
  static BoxDecoration get bottomNavDecoration => const BoxDecoration(
    color: backgroundPrimary,
    border: Border(
      top: BorderSide(
        color: borderPrimary,
        width: 0.5,
      ),
    ),
  );

  /// 标签芯片装饰
  static BoxDecoration tagChipDecoration(Color color) => BoxDecoration(
    color: color.withValues(alpha: opacityHigh),
    borderRadius: BorderRadius.circular(18),
  );

  /// 消息气泡装饰
  static BoxDecoration messageBubbleDecoration({
    required bool isUser,
    Color? customColor,
  }) => BoxDecoration(
    color: customColor ?? (isUser ? messageBubbleUser : messageBubbleAI),
    borderRadius: borderRadiusL,
    boxShadow: [shadowLight],
  );

  /// 按钮装饰
  static BoxDecoration get primaryButtonDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: borderRadiusRound,
    boxShadow: [shadowPrimary],
  );

  static BoxDecoration get secondaryButtonDecoration => BoxDecoration(
    color: backgroundCard,
    borderRadius: borderRadiusRound,
    border: Border.all(color: borderSecondary),
  );

  /// 导航栏图标装饰
  static BoxDecoration navIconDecoration({required bool isSelected}) => BoxDecoration(
    gradient: isSelected ? primaryGradient : null,
    borderRadius: BorderRadius.circular(radiusXL),
    boxShadow: isSelected ? [shadowPrimaryLight] : null,
  );

  /// Profile页面专用装饰
  static BoxDecoration get profileUserCardDecoration => BoxDecoration(
    color: profileCardBackground,
    borderRadius: borderRadiusXXL,
  );

  static BoxDecoration get profileEditButtonDecoration => BoxDecoration(
    color: primaryYellow,
    borderRadius: BorderRadius.all(Radius.circular(50)),
  );

  static BoxDecoration get profileSettingsItemDecoration => BoxDecoration(
    color: backgroundPrimary,
    borderRadius: BorderRadius.all(Radius.circular(18)),
    border: Border.all(
      color: primaryBlack,
      width: 1.5,
    ),
  );

  // ==================== 预定义组件样式 ====================

  /// AppBar样式
  static AppBarTheme get appBarTheme => const AppBarTheme(
    backgroundColor: backgroundPrimary,
    elevation: 0,
    titleTextStyle: TextStyle(
      color: textPrimary,
      fontSize: fontSizeLarge,
      fontWeight: fontWeightSemiBold,
    ),
    iconTheme: IconThemeData(color: textPrimary),
  );

  /// 输入框主题
  static InputDecorationTheme get inputDecorationTheme => InputDecorationTheme(
    filled: true,
    fillColor: Colors.white.withValues(alpha: 0.9),
    border: OutlineInputBorder(
      borderRadius: borderRadiusRound,
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: borderRadiusRound,
      borderSide: BorderSide.none,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: borderRadiusRound,
      borderSide: const BorderSide(color: primaryYellow, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(
      horizontal: spaceL,
      vertical: spaceS,
    ),
    hintStyle: const TextStyle(color: textHint),
  );

  /// 按钮主题
  static ElevatedButtonThemeData get elevatedButtonTheme => ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: primaryYellow,
      foregroundColor: textOnPrimary,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadiusRound,
      ),
      minimumSize: const Size(double.infinity, buttonHeightL),
      textStyle: buttonText,
    ),
  );

  /// 文本按钮主题
  static TextButtonThemeData get textButtonTheme => TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: primaryYellow,
      textStyle: const TextStyle(
        fontSize: fontSizeMedium,
        fontWeight: fontWeightSemiBold,
      ),
    ),
  );

  // ==================== 工具方法 ====================

  /// 根据首字母获取标签颜色
  static Color getTagColorByFirstLetter(String text) {
    if (text.isEmpty) return Colors.grey.withValues(alpha: opacityHigh);

    final firstChar = text.toLowerCase()[0];

    switch (firstChar) {
      case 'a':
      case 'b':
        return Colors.red.withValues(alpha: opacityHigh);
      case 'c':
      case 'd':
        return Colors.orange.withValues(alpha: opacityHigh);
      case 'e':
      case 'f':
        return Colors.amber.withValues(alpha: opacityHigh);
      case 'g':
      case 'h':
        return Colors.green.withValues(alpha: opacityHigh);
      case 'i':
      case 'j':
        return Colors.teal.withValues(alpha: opacityHigh);
      case 'k':
      case 'l':
        return Colors.cyan.withValues(alpha: opacityHigh);
      case 'm':
      case 'n':
        return Colors.blue.withValues(alpha: opacityHigh);
      case 'o':
      case 'p':
        return Colors.indigo.withValues(alpha: opacityHigh);
      case 'q':
      case 'r':
        return Colors.purple.withValues(alpha: opacityHigh);
      case 's':
      case 't':
        return Colors.pink.withValues(alpha: opacityHigh);
      case 'u':
      case 'v':
        return Colors.deepPurple.withValues(alpha: opacityHigh);
      case 'w':
      case 'x':
        return Colors.brown.withValues(alpha: opacityHigh);
      case 'y':
      case 'z':
        return Colors.blueGrey.withValues(alpha: opacityHigh);
      default:
        return Colors.grey.withValues(alpha: opacityHigh);
    }
  }

  /// 创建渐变背景装饰
  static BoxDecoration createGradientDecoration({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topCenter,
    AlignmentGeometry end = Alignment.bottomCenter,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors,
        begin: begin,
        end: end,
      ),
      borderRadius: borderRadius,
      boxShadow: boxShadow,
    );
  }

  /// 创建带透明度的颜色
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
}
